html {
  font-size: 37.5px;
}

.page {
  position: relative;
  width: 51.2rem;
  height: 108.694rem;
  overflow: hidden;
}

.box_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 51.147rem;
  height: 108.694rem;
  margin-left: 0.054rem;
}

.section_1 {
  background-color: rgba(0, 0, 0, 1);
  position: relative;
  width: 51.2rem;
  height: 13.867rem;
  overflow: hidden;
}

.block_1 {
  height: 28.027rem;
  background: url(./img/SketchPngc22cd3bfbdf2b4c82b16fe88718024391d5b03af75c1b89a6da888553145132a.png)
    0rem 6.667rem no-repeat;
  background-size: 42.054rem 13.867rem;
  width: 42.027rem;
  position: absolute;
  left: 4.587rem;
  top: -6.666rem;
}

.section_6 {
  width: 4.587rem;
  height: 1.174rem;
  margin: 7.227rem 0 0 7.84rem;
}

.group_1 {
  border-radius: 50%;
  background-image: url(./img/6002fce2fbf946d3849ae479605d68ed_mergeImage.png);
  width: 1.12rem;
  height: 1.12rem;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 0.054rem;
}

.text_1 {
  width: 3.147rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
}

.text-wrapper_11 {
  width: 5.12rem;
  height: 1.28rem;
  margin: 6.56rem 0 0 18.507rem;
}

.text_2 {
  width: 5.12rem;
  height: 1.28rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.28rem;
  font-family: SourceHanSansCN-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.92rem;
}

.text-wrapper_12 {
  width: 6.32rem;
  height: 0.88rem;
  margin: 0.187rem 0 10.72rem 17.867rem;
}

.text_3 {
  width: 6.32rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.64rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
}

.block_2 {
  background-color: rgba(9, 9, 9, 1);
  position: absolute;
  left: 11.787rem;
  top: 2.454rem;
  width: 39.414rem;
  height: 2.454rem;
}

.text_4 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 1.334rem;
}

.text_5 {
  width: 1.44rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 1.707rem;
}

.text_6 {
  width: 0.96rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.854rem 0 0 1.707rem;
}

.text-wrapper_3 {
  background-color: rgba(255, 255, 255, 1);
  height: 2.454rem;
  width: 3.254rem;
  margin: 0 11.92rem 0 15.2rem;
}

.text_7 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
  margin: 0.827rem 0 0 0.694rem;
}

.block_3 {
  background-color: rgba(255, 255, 255, 1);
  position: absolute;
  left: 18.107rem;
  top: 0;
  width: 33.067rem;
  height: 2.454rem;
}

.text_8 {
  width: 1.84rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.534rem;
  margin: 0.934rem 0 0 2.88rem;
}

.image_1 {
  width: 1.654rem;
  height: 0.027rem;
  margin: 1.227rem 0 0 0.214rem;
}

.text-wrapper_4 {
  background-color: rgba(14, 107, 228, 1);
  height: 2.454rem;
  margin-left: -0.026rem;
  width: 2.747rem;
}

.text_9 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 0.534rem;
}

.text_10 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 0.507rem;
}

.text_11 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.text_12 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.text_13 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin: 0.907rem 0 0 1.067rem;
}

.thumbnail_1 {
  width: 0.374rem;
  height: 0.374rem;
  margin: 1.04rem 11.894rem 0 1.067rem;
}

.group_11 {
  position: relative;
  width: 51.147rem;
  height: 94.854rem;
  margin-bottom: 0.027rem;
}

.text_14 {
  width: 23.227rem;
  height: 2.64rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.64rem;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 0.88rem;
  margin: 1.28rem 0 0 13.947rem;
}

.text_15 {
  width: 3.787rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 1.334rem;
  margin: 1.147rem 0 0 23.707rem;
}

.text_16 {
  width: 4.747rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 1.334rem;
  margin: 30.72rem 0 0 23.227rem;
}

.text_17 {
  width: 14.4rem;
  height: 1.174rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  line-height: 0.587rem;
  margin: 0.294rem 0 0 18.614rem;
}

.section_7 {
  width: 31.094rem;
  height: 11.067rem;
  margin: 0.854rem 0 0 10.054rem;
}

.box_2 {
  box-shadow: 0px 0px 8px 0px rgba(86, 86, 86, 0.5);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 10.134rem;
  height: 11.067rem;
}

.image-text_5 {
  width: 9.067rem;
  height: 9.547rem;
  margin: 0.427rem 0 0 0.534rem;
}

.section_4 {
  background-image: url(./img/a7c1fa97577b41ac993c28c674e7202a_mergeImage.png);
  width: 9.067rem;
  height: 4.8rem;
}

.text-group_6 {
  width: 9.067rem;
  height: 4.267rem;
  margin-top: 0.48rem;
}

.text_18 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
}

.paragraph_1 {
  width: 9.067rem;
  height: 3.2rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.534rem;
  margin-top: 0.4rem;
}

.box_3 {
  box-shadow: 0px 0px 8px 0px rgba(86, 86, 86, 0.5);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 10.134rem;
  height: 11.067rem;
  margin-left: 0.427rem;
}

.image-text_6 {
  width: 9.067rem;
  height: 9.547rem;
  margin: 0.427rem 0 0 0.534rem;
}

.group_3 {
  background-image: url(./img/a93624873787473ab557840d124464f1_mergeImage.png);
  width: 9.067rem;
  height: 4.8rem;
}

.text-group_7 {
  width: 9.067rem;
  height: 4.267rem;
  margin-top: 0.48rem;
}

.text_19 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
}

.text_20 {
  width: 9.067rem;
  height: 3.2rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.534rem;
  margin-top: 0.4rem;
}

.box_4 {
  box-shadow: 0px 0px 8px 0px rgba(86, 86, 86, 0.5);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  width: 10.134rem;
  height: 11.067rem;
  margin-left: 0.267rem;
}

.image-text_7 {
  width: 9.067rem;
  height: 7.414rem;
  margin: 0.427rem 0 0 0.534rem;
}

.section_5 {
  background-image: url(./img/8d72b24f0aff4fd98776998cfe0489a4_mergeImage.png);
  width: 9.067rem;
  height: 4.8rem;
}

.text-group_8 {
  width: 9.067rem;
  height: 2.134rem;
  margin-top: 0.48rem;
}

.text_21 {
  width: 1.894rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.667rem;
}

.text_22 {
  width: 9.067rem;
  height: 1.067rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 0.534rem;
  margin-top: 0.4rem;
}

.section_8 {
  width: 2.214rem;
  height: 0.534rem;
  margin: 19.974rem 0 0 14.134rem;
}

.text_23 {
  width: 1.494rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 0.534rem;
}

.thumbnail_2 {
  width: 0.187rem;
  height: 0.374rem;
  margin-top: 0.107rem;
}

.image-wrapper_1 {
  border-radius: 6px;
  background-image: url(./img/90b2806365c44ec4ae6a3708ca84c279_mergeImage.png);
  height: 13.334rem;
  width: 22.934rem;
  margin: 0.347rem 0 0 14.134rem;
}

.image_2 {
  width: 2.667rem;
  height: 2.667rem;
  margin: 5.334rem 0 0 10.134rem;
}

.group_5 {
  height: 6.134rem;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 27.467rem;
  position: relative;
  margin: 2.667rem 0 0.027rem 11.76rem;
}

.text-wrapper_13 {
  width: 10.907rem;
  height: 0.587rem;
  margin: 0.48rem 0 0 1.6rem;
}

.text_24 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text_25 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text_26 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text-wrapper_14 {
  width: 11.814rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_27 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text_28 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.934rem;
}

.text_29 {
  width: 2.534rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
  margin-left: 2.987rem;
}

.text-wrapper_15 {
  width: 1.68rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_30 {
  width: 1.68rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.text-wrapper_16 {
  width: 0.854rem;
  height: 0.587rem;
  margin: 0.32rem 0 0 1.867rem;
}

.text_31 {
  width: 0.854rem;
  height: 0.587rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.587rem;
}

.group_12 {
  width: 4.747rem;
  height: 1.467rem;
  margin: 0.8rem 0 0.08rem 0.534rem;
}

.image-text_8 {
  width: 4.747rem;
  height: 1.467rem;
}

.image_3 {
  width: 1.494rem;
  height: 1.467rem;
}

.text-group_4 {
  width: 3.147rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.8rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.12rem;
  margin-top: 0.08rem;
}

.group_7 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 6.294rem;
  top: 4.534rem;
  width: 33.094rem;
  height: 1.6rem;
}

.image_4 {
  position: absolute;
  left: 20.88rem;
  top: 37.894rem;
  width: 9.44rem;
  height: 0.107rem;
}

.group_8 {
  position: absolute;
  left: -0.053rem;
  top: 19.6rem;
  width: 51.2rem;
  height: 16rem;
  background: url(./img/SketchPng51cb4f1a65f9f9f2a80566ab0b76c9f8a77b108733ff7084a80bc3c141a63786.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.group_9 {
  background-image: url(./img/89c9827e71bd47e995825799d1464712_mergeImage.png);
  position: absolute;
  left: -0.053rem;
  top: 53.44rem;
  width: 51.2rem;
  height: 16.8rem;
}

.group_10 {
  box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.25);
  position: absolute;
  left: 11.867rem;
  top: 7.014rem;
  width: 27.467rem;
  height: 60.774rem;
}

.box_5 {
  border-radius: 8px;
  background-image: url(./img/b8d447e677534c7b957e2e9fc11992af_mergeImage.png);
  width: 27.467rem;
  height: 10.987rem;
}

.text-group_9 {
  width: 14.4rem;
  height: 7.76rem;
  margin: 1.254rem 0 0 11.654rem;
}

.text_32 {
  width: 4.747rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 1.334rem;
}

.paragraph_2 {
  width: 14.4rem;
  height: 5.867rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 0.587rem;
  margin-top: 0.56rem;
}

.text_33 {
  width: 1.894rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 1.334rem;
  margin: 3.307rem 0 0 -0.027rem;
}

.box_6 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  height: 10.667rem;
  width: 27.467rem;
  margin: 0.614rem 0 0 -0.054rem;
}

.block_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 27.467rem;
  height: 10.667rem;
}

.text-group_10 {
  width: 14.4rem;
  height: 7.174rem;
  margin: 0.987rem 0 0 1.067rem;
}

.text_34 {
  width: 10.4rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  white-space: nowrap;
  line-height: 1.334rem;
}

.paragraph_3 {
  width: 14.4rem;
  height: 5.28rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 0.587rem;
  margin-top: 0.56rem;
}

.image_5 {
  width: 9.6rem;
  height: 9.6rem;
  margin: 0.614rem 1.414rem 0 0.987rem;
}

.text_35 {
  width: 9.414rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.96rem;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: right;
  white-space: nowrap;
  line-height: 1.334rem;
  margin: 20.987rem 0 0 8.987rem;
}

.text-wrapper_10 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  height: 10.934rem;
  width: 24.534rem;
  margin: 0.614rem 0 0 1.414rem;
}

.paragraph_4 {
  width: 22.4rem;
  height: 9.387rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: justify;
  line-height: 0.587rem;
  margin: 0.72rem 0 0 1.067rem;
}

.image_6 {
  position: absolute;
  left: -0.053rem;
  top: 14.267rem;
  width: 1.28rem;
  height: 0.107rem;
}

.image_7 {
  position: absolute;
  left: 19.787rem;
  top: 5.76rem;
  width: 11.627rem;
  height: 0.107rem;
}
