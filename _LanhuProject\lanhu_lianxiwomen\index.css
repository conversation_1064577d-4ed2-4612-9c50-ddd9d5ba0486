.page {
  position: relative;
  width: 1920px;
  height: 2102px;
  overflow: hidden;
}

.group_1 {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 2102px;
}

.box_15 {
  width: 1453px;
  height: 92px;
}

.section_1 {
  border-radius: 50%;
  background-image: url(./img/3a51bdb8986a4010a237ffc821e616ff_mergeImage.png);
  width: 42px;
  height: 42px;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 23px;
}

.text_1 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin: 21px 0 0 12px;
}

.section_2 {
  background-color: rgba(255, 255, 255, 1);
  width: 1240px;
  height: 92px;
  margin-left: 41px;
}

.text_2 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: <PERSON>babaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 266px;
}

.text_3 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 39px;
}

.text_4 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.text_5 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.text_6 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.thumbnail_1 {
  width: 14px;
  height: 14px;
  margin: 39px 446px 0 40px;
}

.box_2 {
  background-color: rgba(9, 9, 9, 1);
  width: 1478px;
  height: 92px;
  margin-left: 442px;
}

.text_7 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 50px;
}

.text_8 {
  width: 54px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 64px;
}

.text_9 {
  width: 36px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 64px;
}

.text-wrapper_1 {
  background-color: rgba(255, 255, 255, 1);
  height: 92px;
  width: 122px;
  margin: 0 447px 0 570px;
}

.text_10 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 31px 0 0 26px;
}

.box_16 {
  width: 170px;
  height: 22px;
  margin: 12px 0 0 460px;
}

.text_11 {
  width: 84px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.thumbnail_2 {
  width: 18px;
  height: 18px;
  margin: 1px 0 0 6px;
}

.text_12 {
  width: 56px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 85, 195, 1);
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 6px;
}

.box_4 {
  background-color: rgba(0, 0, 0, 1);
  width: 1920px;
  height: 200px;
  margin-top: 12px;
}

.group_8 {
  width: 864px;
  height: 102px;
  margin: 37px 0 0 460px;
}

.image_1 {
  width: 70px;
  height: 69px;
}

.text-wrapper_15 {
  width: 774px;
  height: 102px;
}

.text_13 {
  width: 120px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 30px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
}

.text_14 {
  width: 774px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 25px;
  margin-top: 10px;
}

.box_6 {
  background-color: rgba(255, 67, 0, 1);
  width: 1030px;
  height: 4px;
  margin: 57px 0 0 445px;
}

.text-wrapper_3 {
  background-color: rgba(14, 107, 228, 1);
  border-radius: 0px 0px 25px 25px;
  height: 50px;
  width: 1030px;
  margin: 40px 0 0 445px;
}

.text_15 {
  width: 96px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 9px 0 0 60px;
}

.box_17 {
  width: 1030px;
  height: 780px;
  margin: 168px 0 0 445px;
}

.group_2 {
  width: 1px;
  height: 780px;
  border: 1px solid rgba(0, 0, 0, 1);
}

.box_18 {
  width: 668px;
  height: 705px;
  margin: 49px 0 0 180px;
}

.box_19 {
  width: 363px;
  height: 20px;
  margin-left: 10px;
}

.text-wrapper_4 {
  width: 24px;
  height: 20px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_16 {
  width: 24px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_17 {
  width: 24px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text-wrapper_5 {
  width: 24px;
  height: 20px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_18 {
  width: 24px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_19 {
  width: 24px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.image-wrapper_3 {
  width: 668px;
  height: 34px;
  margin-top: 1px;
}

.image_2 {
  width: 329px;
  height: 34px;
}

.image_3 {
  width: 329px;
  height: 34px;
}

.text_20 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 19px 0 0 10px;
}

.image-text_11 {
  width: 668px;
  height: 73px;
  margin-top: 1px;
}

.image_4 {
  width: 668px;
  height: 34px;
}

.text-group_1 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 19px 0 0 10px;
}

.text_21 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_22 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.image-text_12 {
  width: 329px;
  height: 73px;
  margin-top: 1px;
}

.image_5 {
  width: 329px;
  height: 34px;
}

.text-group_2 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 19px 0 0 10px;
}

.text_23 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_24 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.image-text_13 {
  width: 668px;
  height: 73px;
  margin-top: 1px;
}

.image_6 {
  width: 668px;
  height: 34px;
}

.text-group_11 {
  width: 377px;
  height: 20px;
  margin: 19px 0 0 10px;
}

.text-wrapper_6 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_25 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_26 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text-wrapper_7 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_27 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_28 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.image-wrapper_4 {
  width: 668px;
  height: 34px;
  margin-top: 1px;
}

.image_7 {
  width: 329px;
  height: 34px;
}

.image_8 {
  width: 329px;
  height: 34px;
}

.text-wrapper_8 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 19px 0 0 10px;
}

.text_29 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_30 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.image-text_14 {
  width: 668px;
  height: 73px;
  margin-top: 1px;
}

.box_9 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
  width: 668px;
  height: 34px;
  border: 1px solid rgba(211, 211, 211, 1);
}

.text_31 {
  width: 124px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(211, 211, 211, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 6px 0 0 12px;
}

.thumbnail_3 {
  width: 10px;
  height: 6px;
  margin: 14px 12px 0 0;
}

.text-group_4 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 19px 0 0 10px;
}

.text_32 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(53, 53, 53, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_33 {
  width: 38px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(236, 41, 20, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.box_10 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
  width: 668px;
  height: 100px;
  border: 1px solid rgba(211, 211, 211, 1);
  margin-top: 1px;
}

.box_11 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
  width: 668px;
  height: 60px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin-top: 20px;
}

.image-text_15 {
  width: 620px;
  height: 40px;
  margin: 9px 0 0 10px;
}

.thumbnail_4 {
  width: 16px;
  height: 16px;
  margin-top: 3px;
}

.text-group_5 {
  width: 594px;
  height: 40px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 20px;
}

.text-wrapper_9 {
  background-color: rgba(236, 41, 20, 1);
  height: 40px;
  width: 70px;
  margin: 20px 0 0 299px;
}

.text_34 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 9px 0 0 21px;
}

.group_4 {
  width: 1px;
  height: 780px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin-left: 180px;
}

.image_9 {
  width: 1030px;
  height: 44px;
  margin-left: 445px;
}

.box_12 {
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
  height: 260px;
  width: 1030px;
  margin: 40px 0 0 445px;
}

.group_5 {
  background-color: rgba(255, 255, 255, 1);
  width: 1030px;
  height: 260px;
}

.text_35 {
  width: 119px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin: 26px 0 0 80px;
}

.text_36 {
  width: 55px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 18px 0 0 80px;
}

.text-wrapper_10 {
  width: 134px;
  height: 60px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 20px;
  margin: 4px 0 0 80px;
}

.paragraph_1 {
  width: 134px;
  height: 60px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 20px;
}

.text_37 {
  width: 134px;
  height: 60px;
  overflow-wrap: break-word;
  color: rgba(254, 68, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 20px;
}

.text_38 {
  width: 55px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 16px 0 0 80px;
}

.text_39 {
  width: 326px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 4px 0 30px 80px;
}

.box_13 {
  height: 230px;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1030px;
  position: relative;
  margin: 60px 0 0 443px;
}

.text-wrapper_16 {
  width: 409px;
  height: 22px;
  margin: 18px 0 0 60px;
}

.text_40 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_41 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_42 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text-wrapper_17 {
  width: 443px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_43 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_44 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_45 {
  width: 95px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 112px;
}

.text-wrapper_18 {
  width: 63px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_46 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text-wrapper_19 {
  width: 32px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_47 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.group_9 {
  width: 178px;
  height: 55px;
  margin: 30px 0 3px 20px;
}

.image-text_16 {
  width: 178px;
  height: 55px;
}

.image_10 {
  width: 56px;
  height: 55px;
}

.text-group_6 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin-top: 3px;
}

.group_7 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 236px;
  top: 170px;
  width: 1241px;
  height: 60px;
}

.box_20 {
  position: absolute;
  left: 445px;
  top: 560px;
  width: 1030px;
  height: 144px;
}

.section_3 {
  width: 250px;
  height: 144px;
  background: url(./img/SketchPng1e50148ab5077035a7e6852abbedc692cadf67fe771dfa46252d1d455c39de1b.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image_11 {
  width: 50px;
  height: 32px;
  margin: 30px 0 0 100px;
}

.image-text_17 {
  width: 76px;
  height: 20px;
  margin: 28px 0 34px 87px;
}

.text-group_7 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_5 {
  width: 10px;
  height: 6px;
  margin-top: 7px;
}

.section_4 {
  background-color: rgba(255, 255, 255, 1);
  width: 250px;
  height: 128px;
  border: 1px solid rgba(10, 10, 10, 1);
  margin-left: 10px;
}

.label_1 {
  width: 32px;
  height: 32px;
  margin: 30px 0 0 109px;
}

.image-text_18 {
  width: 62px;
  height: 20px;
  margin: 28px 0 18px 94px;
}

.text-group_8 {
  width: 42px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_6 {
  width: 10px;
  height: 6px;
  margin-top: 7px;
}

.section_5 {
  background-color: rgba(255, 255, 255, 1);
  width: 250px;
  height: 128px;
  border: 1px solid rgba(10, 10, 10, 1);
  margin-left: 10px;
}

.label_2 {
  width: 37px;
  height: 32px;
  margin: 30px 0 0 107px;
}

.image-text_19 {
  width: 48px;
  height: 20px;
  margin: 28px 0 18px 101px;
}

.text-group_9 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_7 {
  width: 10px;
  height: 6px;
  margin-top: 7px;
}

.section_6 {
  background-color: rgba(255, 255, 255, 1);
  width: 250px;
  height: 128px;
  border: 1px solid rgba(10, 10, 10, 1);
  margin-left: 10px;
}

.label_3 {
  width: 32px;
  height: 32px;
  margin: 30px 0 0 109px;
}

.image-text_20 {
  width: 76px;
  height: 20px;
  margin: 28px 0 18px 87px;
}

.text-group_10 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_8 {
  width: 10px;
  height: 6px;
  margin-top: 7px;
}
