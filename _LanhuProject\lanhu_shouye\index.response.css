.page {
  position: relative;
  width: 100vw;
  height: 139.17vw;
  overflow: hidden;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 100vw;
  height: 139.17vw;
}

.group_18 {
  width: 85.06vw;
  height: 4.8vw;
}

.text_1 {
  width: 3.6vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin-top: 2.82vw;
}

.box_1 {
  border-radius: 50%;
  background-image: url(./img/79b144a48e324afe8d417c938894a984_mergeImage.png);
  width: 2.19vw;
  height: 2.19vw;
  border: 1px solid rgba(151, 151, 151, 1);
  margin: 1.19vw 0 0 5.78vw;
}

.text_2 {
  width: 6.15vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
  margin: 1.09vw 0 0 0.62vw;
}

.box_2 {
  background-color: rgba(255, 255, 255, 1);
  width: 64.59vw;
  height: 4.8vw;
  margin-left: 2.14vw;
}

.text_3 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 13.85vw;
}

.text_4 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.03vw;
}

.text_5 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.text_6 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.text_7 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 1.77vw 0 0 2.08vw;
}

.thumbnail_1 {
  width: 0.73vw;
  height: 0.73vw;
  margin: 2.03vw 23.22vw 0 2.08vw;
}

.group_19 {
  width: 86.57vw;
  height: 4.8vw;
  margin-left: 13.44vw;
}

.text-wrapper_1 {
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
  background-color: rgba(0, 85, 195, 1);
  height: 4.8vw;
  width: 6.15vw;
}

.text_8 {
  width: 2.82vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 1.66vw;
}

.box_3 {
  background-color: rgba(9, 9, 9, 1);
  position: relative;
  width: 76.05vw;
  height: 4.8vw;
}

.text_9 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 1.66vw;
}

.text_10 {
  width: 2.82vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 2.6vw;
}

.text_11 {
  width: 1.88vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.66vw 0 0 2.6vw;
}

.text-wrapper_2 {
  background-color: rgba(255, 255, 255, 1);
  height: 4.8vw;
  width: 6.36vw;
  margin: 0 23.28vw 0 31.14vw;
}

.text_12 {
  width: 3.7vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
  margin: 1.61vw 0 0 1.35vw;
}

.image_1 {
  position: absolute;
  left: -4.37vw;
  top: 2.35vw;
  width: 4.9vw;
  height: 0.06vw;
}

.group_3 {
  background-image: url(./img/aedda77a45764f4a9a84182ecfa9e1d7_mergeImage.png);
  width: 75vw;
  height: 19.9vw;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-left: 12.5vw;
}

.text_13 {
  width: 43.65vw;
  height: 3.39vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 2.5vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 3.39vw;
  margin: 4.79vw 0 0 15.2vw;
}

.text-wrapper_3 {
  background-color: rgba(236, 41, 20, 1);
  height: 2.4vw;
  width: 5.21vw;
  margin: 2.86vw 0 0 34.89vw;
}

.text_14 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 0.52vw 0 0 0.98vw;
}

.image_2 {
  width: 6.67vw;
  height: 0.73vw;
  margin: 4.73vw 0 0.98vw 34.16vw;
}

.group_4 {
  background-image: url(./img/744457cc39614e1b9eb6e9397ab27d32_mergeImage.png);
  height: 35.42vw;
  margin-top: 3.13vw;
  width: 100vw;
}

.text-wrapper_25 {
  width: 6.2vw;
  height: 2.19vw;
  margin: 2.96vw 0 0 46.92vw;
}

.text_15 {
  width: 6.2vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
}

.text-wrapper_26 {
  width: 35.53vw;
  height: 1.05vw;
  margin: 1.66vw 0 0 32.23vw;
}

.text_16 {
  width: 5.79vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.text_17 {
  width: 2.92vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin-left: 4.17vw;
}

.text_18 {
  width: 2.92vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin-left: 4.17vw;
}

.text_19 {
  width: 4.33vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin-left: 4.17vw;
}

.text_20 {
  width: 2.92vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin-left: 4.17vw;
}

.box_9 {
  width: 5.47vw;
  height: 5.32vw;
  margin: 2.03vw 0 0 72.65vw;
}

.group_5 {
  box-shadow: 0px 0px 6px 0px rgba(113, 113, 113, 0.3);
  border-radius: 8px;
  background-image: url(./img/d95c1a527e584dbd8ba4a677be6c22d9_mergeImage.png);
  width: 5.47vw;
  height: 5.32vw;
}

.box_10 {
  width: 5.47vw;
  height: 5.32vw;
  margin: 0.62vw 0 0 72.65vw;
}

.group_6 {
  box-shadow: 0px 0px 6px 0px rgba(113, 113, 113, 0.3);
  border-radius: 8px;
  background-image: url(./img/d84667fe1ad94a8290dd55bdfb3ae3be_mergeImage.png);
  width: 5.47vw;
  height: 5.32vw;
}

.box_11 {
  width: 55.27vw;
  height: 5.32vw;
  margin: 0.62vw 0 0 22.86vw;
}

.text_21 {
  width: 3.6vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin-top: 3.91vw;
}

.box_4 {
  box-shadow: 0px 0px 6px 0px rgba(113, 113, 113, 0.3);
  border-radius: 8px;
  background-image: url(./img/5fa0fd3476574832a0fa7dc2e79e889a_mergeImage.png);
  width: 5.47vw;
  height: 5.32vw;
}

.box_12 {
  width: 55.42vw;
  height: 5.58vw;
  margin: 0.1vw 0 2.65vw 22.7vw;
}

.text-wrapper_6 {
  background-color: rgba(0, 0, 0, 1);
  height: 1.78vw;
  width: 4.07vw;
}

.text_22 {
  width: 2.19vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.31vw 0 0 0.93vw;
}

.image_3 {
  width: 3.7vw;
  height: 2.77vw;
  margin: 1.3vw 0 0 0.83vw;
}

.text-wrapper_7 {
  background-color: rgba(0, 0, 0, 0);
  height: 1.78vw;
  border: 1px solid rgba(0, 0, 0, 1);
  width: 4.07vw;
  margin: 3.33vw 0 0 -0.05vw;
}

.text_23 {
  width: 2.19vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.31vw 0 0 0.93vw;
}

.text-wrapper_8 {
  background-color: rgba(0, 0, 0, 0);
  height: 1.78vw;
  border: 1px solid rgba(0, 0, 0, 1);
  width: 4.07vw;
  margin: 3.33vw 0 0 2.08vw;
}

.text_24 {
  width: 2.19vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
  margin: 0.31vw 0 0 0.93vw;
}

.text-group_16 {
  width: 3.34vw;
  height: 2.5vw;
  margin: 3.07vw 0 0 13.12vw;
}

.text_25 {
  width: 3.34vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.1vw;
}

.text_26 {
  width: 1.25vw;
  height: 0.89vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.62vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.89vw;
  margin-top: 0.47vw;
}

.group_7 {
  width: 0.06vw;
  height: 2.45vw;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 3.12vw 0 0 1.56vw;
}

.text-group_17 {
  width: 3.08vw;
  height: 2.5vw;
  margin: 3.07vw 0 0 1.56vw;
}

.text_27 {
  width: 2.19vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.1vw;
}

.text_28 {
  width: 3.08vw;
  height: 0.89vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.62vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.89vw;
  margin-top: 0.53vw;
}

.group_8 {
  width: 0.06vw;
  height: 2.45vw;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 3.12vw 0 0 1.61vw;
}

.text-group_18 {
  width: 3.23vw;
  height: 2.56vw;
  margin: 3.02vw 0 0 1.56vw;
}

.text-wrapper_9 {
  width: 3.23vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
}

.text_29 {
  width: 3.23vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
}

.text_30 {
  width: 3.23vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
}

.text_31 {
  width: 1.25vw;
  height: 0.89vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.62vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.89vw;
  margin-top: 0.37vw;
}

.group_9 {
  width: 0.06vw;
  height: 2.45vw;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 3.12vw 0 0 1.56vw;
}

.text-group_19 {
  width: 4.33vw;
  height: 2.56vw;
  margin: 3.02vw 0 0 1.56vw;
}

.text-wrapper_10 {
  width: 4.33vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
}

.text_32 {
  width: 4.33vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
}

.text_33 {
  width: 4.33vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.93vw;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.31vw;
}

.text_34 {
  width: 1.25vw;
  height: 0.89vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.62vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 0.89vw;
  margin-top: 0.37vw;
}

.text-group_20 {
  width: 45.37vw;
  height: 6.2vw;
  margin: 2.96vw 0 0 26.71vw;
}

.text_35 {
  width: 18.49vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.61vw;
  margin-left: 14.07vw;
}

.text_36 {
  width: 45.37vw;
  height: 2.82vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: right;
  line-height: 1.41vw;
  margin-top: 0.79vw;
}

.group_10 {
  background-color: rgba(0, 0, 0, 1);
  width: 53.65vw;
  height: 6.25vw;
  margin: 2.91vw 0 0 22.55vw;
}

.text_37 {
  width: 5vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.25vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.72vw;
  margin: 2.23vw 0 0 10.72vw;
}

.text-wrapper_11 {
  background-image: url(./img/11c09b6d4d974cfdb0174ba7f2c9e083_mergeImage.png);
  height: 6.25vw;
  margin-left: 11.31vw;
  width: 26.62vw;
}

.text_38 {
  width: 5vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.25vw;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 1.72vw;
  margin: 2.23vw 0 0 10.83vw;
}

.group_20 {
  width: 59.38vw;
  height: 2.61vw;
  margin: 2.96vw 0 0 26.56vw;
}

.text_39 {
  width: 7.4vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.87vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.61vw;
}

.image-text_8 {
  width: 4.17vw;
  height: 1.05vw;
  margin: 1.14vw 0 0 37.76vw;
}

.text-group_6 {
  width: 2.92vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(118, 118, 118, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.thumbnail_2 {
  width: 0.73vw;
  height: 0.73vw;
  margin-top: 0.21vw;
}

.image_4 {
  width: 4.02vw;
  height: 0.06vw;
  margin: 1.66vw 0 0 1.04vw;
}

.image-text_9 {
  width: 4.17vw;
  height: 1.05vw;
  margin: 1.14vw 0 0 0.83vw;
}

.text-group_7 {
  width: 2.92vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(0, 58, 133, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.05vw;
}

.thumbnail_3 {
  width: 0.73vw;
  height: 0.73vw;
  margin-top: 0.21vw;
}

.list_3 {
  width: 53.65vw;
  height: 17.61vw;
  justify-content: space-between;
  margin: 1.19vw 0 0 22.55vw;
}

.image-text_3-0 {
  background-color: rgba(244, 244, 244, 1);
  width: 13.03vw;
  height: 17.61vw;
  margin-right: 0.53vw;
}

.group_12-0 {
  background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
  width: 13.03vw;
  height: 6.67vw;
  background: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
}

.text-group_21-0 {
  width: 10.84vw;
  height: 5.47vw;
  margin: 0.52vw 0 4.94vw 1.04vw;
}

.text_40-0 {
  width: 10.84vw;
  height: 2.82vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.41vw;
}

.text_41-0 {
  width: 10.84vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.15vw;
  margin-top: 0.37vw;
}

.image-text_3-1 {
  background-color: rgba(244, 244, 244, 1);
  width: 13.03vw;
  height: 17.61vw;
  margin-right: 0.53vw;
}

.group_12-1 {
  background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
  width: 13.03vw;
  height: 6.67vw;
  background: url(./img/6e327735808b4c35be47442543b0094a_mergeImage.png);
}

.text-group_21-1 {
  width: 10.84vw;
  height: 5.47vw;
  margin: 0.52vw 0 4.94vw 1.04vw;
}

.text_40-1 {
  width: 10.84vw;
  height: 2.82vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.41vw;
}

.text_41-1 {
  width: 10.84vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.15vw;
  margin-top: 0.37vw;
}

.image-text_3-2 {
  background-color: rgba(244, 244, 244, 1);
  width: 13.03vw;
  height: 17.61vw;
  margin-right: 0.53vw;
}

.group_12-2 {
  background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
  width: 13.03vw;
  height: 6.67vw;
  background: url(./img/050735b55ad74444aa753f87269e74f6_mergeImage.png);
}

.text-group_21-2 {
  width: 10.84vw;
  height: 5.47vw;
  margin: 0.52vw 0 4.94vw 1.04vw;
}

.text_40-2 {
  width: 10.84vw;
  height: 2.82vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.41vw;
}

.text_41-2 {
  width: 10.84vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.15vw;
  margin-top: 0.37vw;
}

.image-text_3-3 {
  background-color: rgba(244, 244, 244, 1);
  width: 13.03vw;
  height: 17.61vw;
  margin-right: 0.53vw;
}

.group_12-3 {
  background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
  width: 13.03vw;
  height: 6.67vw;
  background: url(./img/a40c4aded9fe41128fc2d24e4588ad00_mergeImage.png);
}

.text-group_21-3 {
  width: 10.84vw;
  height: 5.47vw;
  margin: 0.52vw 0 4.94vw 1.04vw;
}

.text_40-3 {
  width: 10.84vw;
  height: 2.82vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.04vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.41vw;
}

.text_41-3 {
  width: 10.84vw;
  height: 2.3vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 1.15vw;
  margin-top: 0.37vw;
}

.group_13 {
  height: 8.86vw;
  background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 53.65vw;
  margin: 3.12vw 0 0 22.55vw;
}

.text-wrapper_27 {
  width: 4.95vw;
  height: 1.72vw;
  margin: 1.35vw 0 0 2.6vw;
}

.text_42 {
  width: 4.95vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.25vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.72vw;
}

.group_21 {
  width: 44.74vw;
  height: 2.5vw;
  margin: 0.15vw 0 3.12vw 2.6vw;
}

.text_43 {
  width: 33.29vw;
  height: 2.09vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.72vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 1.05vw;
  margin-top: 0.42vw;
}

.text-wrapper_13 {
  background-color: rgba(236, 41, 20, 1);
  height: 2.4vw;
  width: 5.21vw;
}

.text_44 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin: 0.52vw 0 0 0.98vw;
}

.group_15 {
  height: 11.98vw;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 53.65vw;
  position: relative;
  margin: 4.47vw 0 0 22.55vw;
}

.text-wrapper_28 {
  width: 21.31vw;
  height: 1.15vw;
  margin: 0.93vw 0 0 3.12vw;
}

.text_45 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text_46 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text_47 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text-wrapper_29 {
  width: 23.08vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_48 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text_49 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.73vw;
}

.text_50 {
  width: 4.95vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
  margin-left: 5.84vw;
}

.text-wrapper_30 {
  width: 3.29vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_51 {
  width: 3.29vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.text-wrapper_31 {
  width: 1.67vw;
  height: 1.15vw;
  margin: 0.62vw 0 0 3.64vw;
}

.text_52 {
  width: 1.67vw;
  height: 1.15vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.83vw;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.15vw;
}

.group_22 {
  width: 9.28vw;
  height: 2.87vw;
  margin: 1.56vw 0 0.15vw 1.04vw;
}

.image-text_10 {
  width: 9.28vw;
  height: 2.87vw;
}

.image_5 {
  width: 2.92vw;
  height: 2.87vw;
}

.text-group_9 {
  width: 6.15vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.56vw;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 2.19vw;
  margin-top: 0.16vw;
}

.group_17 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 12.82vw;
  top: 8.86vw;
  width: 64.64vw;
  height: 3.13vw;
}
