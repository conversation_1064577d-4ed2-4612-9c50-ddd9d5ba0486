# 山西智诚导航栏组件

一个基于原生HTML、CSS、JavaScript实现的可复用导航栏组件，包含顶部导航栏和二级导航栏。

## 特性

- ✅ 原生JavaScript实现，无需任何框架依赖
- ✅ 样式隔离，使用`zc-`前缀避免样式冲突
- ✅ 响应式设计，支持移动端适配
- ✅ 事件回调机制，支持自定义交互逻辑
- ✅ 活跃状态管理
- ✅ 可配置的图片路径和容器
- ✅ 模板加载失败时的备用方案

## 文件结构

```
component/navigation/
├── navigation.html     # HTML模板
├── navigation.css      # 样式文件
├── navigation.js       # JavaScript逻辑
├── README.md          # 说明文档
└── example.html       # 使用示例
```

## 快速开始

### 1. 引入文件

在HTML页面中引入CSS和JavaScript文件：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题</title>
    <!-- 引入导航栏样式 -->
    <link rel="stylesheet" href="./component/navigation/navigation.css">
</head>
<body>
    <!-- 导航栏容器 -->
    <div id="navigation-container"></div>
    
    <!-- 页面其他内容 -->
    <main>
        <!-- 你的页面内容 -->
    </main>
    
    <!-- 引入导航栏脚本 -->
    <script src="./component/navigation/navigation.js"></script>
    <script>
        // 初始化导航栏
        const navigation = new ZCNavigation({
            container: '#navigation-container'
        });
    </script>
</body>
</html>
```

### 2. 基本配置

```javascript
const navigation = new ZCNavigation({
    container: '#navigation-container',  // 容器选择器或DOM元素
    logoPath: './img/logo.png',         // logo图片路径
    navIconPath: './img/nav-icon.png',  // 导航图标路径
    activeNav: 'about',                 // 默认激活的主导航
    activeProduct: 'list',              // 默认激活的产品导航
    
    // 事件回调
    onNavClick: function(navType, element) {
        console.log('主导航点击:', navType);
        // 处理导航点击逻辑
    },
    
    onProductClick: function(productType, element) {
        console.log('产品导航点击:', productType);
        // 处理产品导航点击逻辑
    },
    
    onContactSales: function(event) {
        console.log('联系销售按钮点击');
        // 处理联系销售逻辑
    }
});
```

## API 参考

### 构造函数选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `container` | string\|Element | 'body' | 组件容器选择器或DOM元素 |
| `logoPath` | string | '../../home/<USER>/...' | Logo图片路径 |
| `navIconPath` | string | './img/...' | 导航图标路径 |
| `activeNav` | string | '' | 默认激活的主导航项 |
| `activeProduct` | string | '' | 默认激活的产品导航项 |
| `onNavClick` | function | null | 主导航点击回调 |
| `onProductClick` | function | null | 产品导航点击回调 |
| `onContactSales` | function | null | 联系销售按钮点击回调 |

### 实例方法

#### `setActiveNav(navType)`
设置活跃的主导航项
- `navType`: 导航类型 ('about', 'jobs', 'solutions', 'news', 'contact')

#### `setActiveProduct(productType)`
设置活跃的产品导航项
- `productType`: 产品类型 ('list', 'parts', 'others')

#### `updateOptions(newOptions)`
更新组件配置
- `newOptions`: 新的配置选项对象

#### `destroy()`
销毁组件，清空容器内容

## 导航项标识

### 主导航项
- `about`: 关于我们
- `jobs`: 招聘信息
- `solutions`: 解决方案
- `news`: 新闻动态
- `contact`: 联系地址

### 产品导航项
- `list`: 产品列表
- `parts`: 零部件
- `others`: 其他

## 样式自定义

组件使用`zc-`前缀来避免样式冲突。如需自定义样式，可以覆盖相应的CSS类：

```css
/* 自定义主导航链接颜色 */
.zc-nav-link {
    color: #your-color !important;
}

/* 自定义联系销售按钮样式 */
.zc-contact-sales-btn {
    background-color: #your-color !important;
}
```

## 响应式支持

组件内置响应式设计：
- 桌面端：完整布局
- 平板端（≤1200px）：调整间距和内边距
- 移动端（≤768px）：垂直布局，隐藏装饰元素

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11+（需要Promise polyfill）

## 注意事项

1. 确保图片路径正确，建议使用相对路径
2. 如果使用模板加载方式，确保服务器支持CORS
3. 组件会自动处理模板加载失败的情况，使用内联模板作为备用方案
4. 建议在DOM加载完成后初始化组件
