<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏组件使用示例 - 山西智诚</title>
    
    <!-- 引入导航栏样式 -->
    <link rel="stylesheet" href="./navigation.css">
    
    <!-- 页面样式 -->
    <style>
        body {
            margin: 0;
            font-family: PingFangSC-Regular, sans-serif;
            background-color: #f5f5f5;
        }
        
        .demo-content {
            max-width: 1025px;
            margin: 40px auto;
            padding: 40px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #0055c3;
            padding-bottom: 10px;
        }
        
        .demo-controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .demo-btn {
            padding: 10px 20px;
            background-color: #0055c3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .demo-btn:hover {
            background-color: #003d8f;
        }
        
        .demo-btn.secondary {
            background-color: #666;
        }
        
        .demo-btn.secondary:hover {
            background-color: #444;
        }
        
        .demo-log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .demo-log-item {
            margin-bottom: 5px;
            color: #495057;
        }
        
        .demo-log-item.success {
            color: #28a745;
        }
        
        .demo-log-item.info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <!-- 导航栏容器 -->
    <div id="navigation-container"></div>
    
    <!-- 演示内容 -->
    <div class="demo-content">
        <div class="demo-section">
            <h2 class="demo-title">导航栏组件演示</h2>
            <p>这是一个完全基于原生HTML、CSS、JavaScript实现的可复用导航栏组件。</p>
        </div>
        
        <div class="demo-section">
            <h3 class="demo-title">控制面板</h3>
            <div class="demo-controls">
                <button class="demo-btn" onclick="setActiveNav('about')">激活"关于我们"</button>
                <button class="demo-btn" onclick="setActiveNav('jobs')">激活"招聘信息"</button>
                <button class="demo-btn" onclick="setActiveNav('solutions')">激活"解决方案"</button>
                <button class="demo-btn" onclick="setActiveNav('news')">激活"新闻动态"</button>
                <button class="demo-btn" onclick="setActiveNav('contact')">激活"联系地址"</button>
            </div>
            <div class="demo-controls">
                <button class="demo-btn secondary" onclick="setActiveProduct('list')">激活"产品列表"</button>
                <button class="demo-btn secondary" onclick="setActiveProduct('parts')">激活"零部件"</button>
                <button class="demo-btn secondary" onclick="setActiveProduct('others')">激活"其他"</button>
            </div>
            <div class="demo-controls">
                <button class="demo-btn" onclick="clearLog()">清空日志</button>
                <button class="demo-btn secondary" onclick="destroyNavigation()">销毁组件</button>
                <button class="demo-btn" onclick="recreateNavigation()">重新创建</button>
            </div>
        </div>
        
        <div class="demo-section">
            <h3 class="demo-title">事件日志</h3>
            <div id="demo-log" class="demo-log">
                <div class="demo-log-item success">导航栏组件已初始化</div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3 class="demo-title">组件特性</h3>
            <ul>
                <li><strong>样式隔离：</strong>使用 zc- 前缀避免样式冲突</li>
                <li><strong>响应式设计：</strong>支持桌面端和移动端</li>
                <li><strong>事件回调：</strong>支持自定义点击事件处理</li>
                <li><strong>状态管理：</strong>支持设置和切换活跃状态</li>
                <li><strong>可配置：</strong>支持自定义图片路径和容器</li>
                <li><strong>容错处理：</strong>模板加载失败时使用备用方案</li>
            </ul>
        </div>
    </div>
    
    <!-- 引入导航栏脚本 -->
    <script src="./navigation.js"></script>
    <script>
        let navigation;
        
        // 初始化导航栏
        function initNavigation() {
            navigation = new ZCNavigation({
                container: '#navigation-container',
                logoPath: '../../home/<USER>/79b144a48e324afe8d417c938894a984_mergeImage.png',
                navIconPath: '../../home/<USER>/SketchPng877a2df19c62106064ae9368d65300ad01e4c332b3c372f32108e55019e19d0c.png',
                activeNav: 'about',
                activeProduct: 'list',
                
                onNavClick: function(navType, element) {
                    addLog(`主导航点击: ${navType}`, 'info');
                },
                
                onProductClick: function(productType, element) {
                    addLog(`产品导航点击: ${productType}`, 'info');
                },
                
                onContactSales: function(event) {
                    addLog('联系销售按钮点击', 'success');
                    alert('联系销售功能被触发！');
                }
            });
        }
        
        // 设置活跃的主导航
        function setActiveNav(navType) {
            if (navigation) {
                navigation.setActiveNav(navType);
                addLog(`设置活跃主导航: ${navType}`, 'success');
            }
        }
        
        // 设置活跃的产品导航
        function setActiveProduct(productType) {
            if (navigation) {
                navigation.setActiveProduct(productType);
                addLog(`设置活跃产品导航: ${productType}`, 'success');
            }
        }
        
        // 销毁导航栏
        function destroyNavigation() {
            if (navigation) {
                navigation.destroy();
                navigation = null;
                addLog('导航栏组件已销毁', 'info');
            }
        }
        
        // 重新创建导航栏
        function recreateNavigation() {
            destroyNavigation();
            setTimeout(() => {
                initNavigation();
                addLog('导航栏组件已重新创建', 'success');
            }, 100);
        }
        
        // 添加日志
        function addLog(message, type = '') {
            const logContainer = document.getElementById('demo-log');
            const logItem = document.createElement('div');
            logItem.className = `demo-log-item ${type}`;
            logItem.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logItem);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('demo-log').innerHTML = '';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initNavigation();
        });
    </script>
</body>
</html>
