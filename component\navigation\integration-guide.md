# 导航栏组件集成指南

## 快速集成步骤

### 1. 文件准备
将以下文件复制到您的项目中：
```
component/navigation/
├── navigation.html
├── navigation.css
├── navigation.js
└── README.md
```

### 2. HTML页面集成

#### 方法一：完整替换（推荐）
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题</title>
    
    <!-- 引入导航栏样式 -->
    <link rel="stylesheet" href="./component/navigation/navigation.css">
    <!-- 其他样式文件 -->
    <link rel="stylesheet" href="./your-styles.css">
</head>
<body>
    <!-- 导航栏容器 -->
    <div id="navigation-container"></div>
    
    <!-- 页面内容 -->
    <main class="main-content">
        <!-- 您的页面内容 -->
    </main>
    
    <!-- 引入导航栏脚本 -->
    <script src="./component/navigation/navigation.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            new ZCNavigation({
                container: '#navigation-container',
                logoPath: './img/logo.png',
                navIconPath: './img/nav-icon.png',
                onNavClick: function(navType) {
                    // 处理导航点击
                    console.log('导航点击:', navType);
                },
                onProductClick: function(productType) {
                    // 处理产品导航点击
                    console.log('产品点击:', productType);
                },
                onContactSales: function() {
                    // 处理联系销售点击
                    console.log('联系销售点击');
                }
            });
        });
    </script>
</body>
</html>
```

#### 方法二：现有页面改造
如果您已有页面，需要替换现有导航栏：

1. **移除原有导航栏HTML**：
```html
<!-- 删除这些部分 -->
<header class="header">...</header>
<section class="secondary-nav">...</section>
```

2. **添加组件容器**：
```html
<!-- 在原导航栏位置添加 -->
<div id="navigation-container"></div>
```

3. **隐藏原有导航栏样式**：
```css
/* 在您的CSS中添加 */
.header,
.secondary-nav {
    display: none;
}
```

### 3. 样式调整

#### 避免样式冲突
组件使用 `zc-` 前缀，但如果仍有冲突，可以：

```css
/* 提高组件样式优先级 */
.zc-navigation-wrapper .zc-header {
    /* 您的自定义样式 */
}

/* 或使用 !important */
.zc-nav-link {
    color: #your-color !important;
}
```

#### 响应式适配
组件已内置响应式设计，但您可以根据需要调整：

```css
@media (max-width: 768px) {
    .zc-navigation-wrapper {
        /* 移动端自定义样式 */
    }
}
```

### 4. JavaScript事件处理

#### 基本事件处理
```javascript
const navigation = new ZCNavigation({
    container: '#navigation-container',
    
    onNavClick: function(navType, element) {
        // 主导航点击处理
        switch(navType) {
            case 'about':
                window.location.href = '/about';
                break;
            case 'jobs':
                window.location.href = '/jobs';
                break;
            // ... 其他导航项
        }
    },
    
    onProductClick: function(productType, element) {
        // 产品导航点击处理
        if (productType === 'list') {
            // 滚动到产品区域
            document.querySelector('.product-section').scrollIntoView({
                behavior: 'smooth'
            });
        }
    },
    
    onContactSales: function(event) {
        // 联系销售处理
        // 可以打开模态框、跳转页面或滚动到联系区域
        document.querySelector('.contact-section').scrollIntoView({
            behavior: 'smooth'
        });
    }
});
```

#### 动态控制
```javascript
// 设置活跃状态
navigation.setActiveNav('about');
navigation.setActiveProduct('list');

// 更新配置
navigation.updateOptions({
    activeNav: 'news',
    onNavClick: newClickHandler
});
```

### 5. 图片资源处理

确保图片路径正确：
```javascript
new ZCNavigation({
    logoPath: './img/logo.png',           // Logo图片
    navIconPath: './img/nav-icon.png',    // 导航图标
    // ...
});
```

### 6. 常见问题解决

#### 问题1：组件不显示
- 检查容器元素是否存在
- 检查CSS文件是否正确引入
- 检查JavaScript是否有错误

#### 问题2：样式冲突
- 使用浏览器开发者工具检查样式覆盖
- 增加CSS选择器优先级
- 使用 `!important` 强制应用样式

#### 问题3：图片不显示
- 检查图片路径是否正确
- 确保图片文件存在
- 检查服务器是否正确提供图片资源

#### 问题4：事件不触发
- 检查事件回调函数是否正确定义
- 使用浏览器控制台查看错误信息
- 确保DOM已加载完成再初始化组件

### 7. 性能优化建议

1. **延迟加载**：
```javascript
// 在需要时才初始化组件
function initNavigation() {
    if (!window.navigationInitialized) {
        new ZCNavigation({...});
        window.navigationInitialized = true;
    }
}
```

2. **缓存DOM查询**：
```javascript
const container = document.getElementById('navigation-container');
new ZCNavigation({
    container: container  // 传入DOM元素而不是选择器
});
```

3. **事件委托**：
组件内部已使用事件委托，无需额外处理。

### 8. 浏览器兼容性

- 现代浏览器：完全支持
- IE 11：需要Promise polyfill
- 移动浏览器：完全支持

如需支持IE 11，添加polyfill：
```html
<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"></script>
```

### 9. 调试技巧

1. **开启控制台日志**：
```javascript
new ZCNavigation({
    // ...
    onNavClick: function(navType) {
        console.log('Navigation clicked:', navType);
    }
});
```

2. **检查组件状态**：
```javascript
// 在浏览器控制台中
console.log(navigation);
```

3. **样式调试**：
使用浏览器开发者工具的Elements面板检查样式应用情况。
