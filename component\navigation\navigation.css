/* 导航栏组件样式 - 使用 zc- 前缀避免样式冲突 */

/* 组件容器 */
.zc-navigation-wrapper {
    width: 100%;
    background-color: #ffffff;
    font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
}

/* 重置组件内部元素的默认样式 */
.zc-navigation-wrapper * {
    box-sizing: border-box;
    flex-shrink: 0;
}

.zc-navigation-wrapper button {
    margin: 0;
    padding: 0;
    border: 1px solid transparent;
    outline: none;
    background-color: transparent;
    cursor: pointer;
}

.zc-navigation-wrapper button:active {
    opacity: 0.6;
}

.zc-navigation-wrapper a {
    text-decoration: none;
}

/* 顶部导航栏样式 */
.zc-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 23px 0;
    height: 92px;
    background-color: #ffffff;
    width: 1025px;
    margin: 0 auto;
}

.zc-logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.zc-logo-icon {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background-image: url('../../home/<USER>/79b144a48e324afe8d417c938894a984_mergeImage.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.zc-company-name {
    font-size: 30px;
    font-family: AlibabaPuHuiTi-Medium, sans-serif;
    font-weight: 500;
    color: #000000;
    margin: 0;
    line-height: 42px;
}

.zc-main-nav {
    display: flex;
    align-items: center;
    gap: 40px;
}

.zc-nav-link {
    font-size: 16px;
    font-family: AlibabaPuHuiTi-Regular, sans-serif;
    color: #000000;
    text-decoration: none;
    line-height: 22px;
    transition: color 0.3s ease;
    position: relative;
}

.zc-nav-link:hover {
    color: #0055c3;
}

.zc-nav-link.active {
    color: #0055c3;
    font-weight: 500;
}

.zc-nav-icon {
    width: 14px;
    height: 14px;
    margin-left: 40px;
}

/* 二级导航栏样式 */
.zc-secondary-nav {
    display: flex;
    align-items: center;
    width: 100%;
    height: 92px;
}

.zc-secondary-nav-left {
    flex: 1;
    background-color: transparent;
    height: 100%;
}

.zc-secondary-nav-right {
    flex: 1;
    background-color: #000;
    height: 100%;
}

.zc-product-nav {
    display: flex;
    align-items: center;
    background-color: #000;
    height: 92px;
    width: 1025px;
    position: relative;
    padding: 0 32px;
    gap: 50px;
}

.zc-product-link {
    font-size: 18px;
    font-family: AlibabaPuHuiTi-Regular, sans-serif;
    color: #ffffff;
    text-decoration: none;
    line-height: 25px;
    transition: color 0.3s ease;
    position: relative;
}

.zc-product-link:hover {
    color: #0055c3;
}

.zc-product-link.active {
    color: #0055c3;
}

.zc-contact-sales-btn {
    background-color: #ffffff;
    color: #000000;
    font-size: 18px;
    font-family: AlibabaPuHuiTi-Regular, sans-serif;
    line-height: 30px;
    border: none;
    padding: 31px 26px;
    margin-left: auto;
    margin-right: 32px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.zc-contact-sales-btn:hover {
    background-color: #f0f0f0;
    transform: translateY(-1px);
}

.zc-contact-sales-btn:active {
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .zc-header,
    .zc-product-nav {
        width: 100%;
        max-width: 1025px;
        padding: 0 20px;
    }
    
    .zc-main-nav {
        gap: 20px;
    }
    
    .zc-product-nav {
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .zc-header {
        flex-direction: column;
        height: auto;
        padding: 15px 20px;
        gap: 15px;
    }
    
    .zc-main-nav {
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
    }
    
    .zc-nav-icon {
        margin-left: 0;
    }
    
    .zc-secondary-nav {
        height: auto;
        flex-direction: column;
    }
    
    .zc-secondary-nav-left,
    .zc-secondary-nav-right {
        display: none;
    }
    
    .zc-product-nav {
        width: 100%;
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
        padding: 20px;
        height: auto;
    }
    
    .zc-contact-sales-btn {
        margin: 0;
        padding: 15px 20px;
    }
}
