/**
 * 山西智诚导航栏组件
 * 原生JavaScript实现的可复用导航栏组件
 */

class ZCNavigation {
    constructor(options = {}) {
        this.options = {
            container: options.container || 'body',
            logoPath: options.logoPath || '../../home/<USER>/79b144a48e324afe8d417c938894a984_mergeImage.png',
            navIconPath: options.navIconPath || './img/SketchPng877a2df19c62106064ae9368d65300ad01e4c332b3c372f32108e55019e19d0c.png',
            onNavClick: options.onNavClick || null,
            onProductClick: options.onProductClick || null,
            onContactSales: options.onContactSales || null,
            activeNav: options.activeNav || '',
            activeProduct: options.activeProduct || ''
        };
        
        this.navigationHTML = '';
        this.init();
    }

    // 初始化组件
    init() {
        this.loadTemplate()
            .then(() => {
                this.render();
                this.bindEvents();
                this.setActiveStates();
            })
            .catch(error => {
                console.error('导航栏组件初始化失败:', error);
            });
    }

    // 加载HTML模板
    async loadTemplate() {
        try {
            const response = await fetch('./component/navigation/navigation.html');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.navigationHTML = await response.text();
            
            // 替换图片路径
            this.navigationHTML = this.navigationHTML
                .replace(/src="\.\/img\/SketchPng877a2df19c62106064ae9368d65300ad01e4c332b3c372f32108e55019e19d0c\.png"/, 
                        `src="${this.options.navIconPath}"`);
                        
        } catch (error) {
            console.error('加载导航栏模板失败:', error);
            // 如果加载失败，使用内联模板
            this.navigationHTML = this.getInlineTemplate();
        }
    }

    // 内联模板（备用方案）
    getInlineTemplate() {
        return `
        <div class="zc-navigation-wrapper">
            <header class="zc-header">
                <div class="zc-logo-section">
                    <div class="zc-logo-icon"></div>
                    <h1 class="zc-company-name">山西智诚</h1>
                </div>
                <nav class="zc-main-nav">
                    <a href="#" class="zc-nav-link" data-nav="about">关于我们</a>
                    <a href="#" class="zc-nav-link" data-nav="jobs">招聘信息</a>
                    <a href="#" class="zc-nav-link" data-nav="solutions">解决方案</a>
                    <a href="#" class="zc-nav-link" data-nav="news">新闻动态</a>
                    <a href="#" class="zc-nav-link" data-nav="contact">联系地址</a>
                    <img class="zc-nav-icon" src="${this.options.navIconPath}" alt="导航图标" />
                </nav>
            </header>
            <section class="zc-secondary-nav">
                <div class="zc-secondary-nav-left"></div>
                <nav class="zc-product-nav">
                    <a href="#" class="zc-product-link" data-product="list">产品列表</a>
                    <a href="#" class="zc-product-link" data-product="parts">零部件</a>
                    <a href="#" class="zc-product-link" data-product="others">其他</a>
                    <button class="zc-contact-sales-btn">联系销售</button>
                </nav>
                <div class="zc-secondary-nav-right"></div>
            </section>
        </div>`;
    }

    // 渲染组件到页面
    render() {
        const container = typeof this.options.container === 'string' 
            ? document.querySelector(this.options.container)
            : this.options.container;
            
        if (!container) {
            console.error('找不到指定的容器元素');
            return;
        }

        container.innerHTML = this.navigationHTML;
        
        // 更新logo图片路径
        const logoIcon = container.querySelector('.zc-logo-icon');
        if (logoIcon) {
            logoIcon.style.backgroundImage = `url('${this.options.logoPath}')`;
        }
    }

    // 绑定事件
    bindEvents() {
        const container = typeof this.options.container === 'string' 
            ? document.querySelector(this.options.container)
            : this.options.container;

        if (!container) return;

        // 主导航点击事件
        const navLinks = container.querySelectorAll('.zc-nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const navType = link.getAttribute('data-nav');
                
                // 更新活跃状态
                this.setActiveNav(navType);
                
                // 执行回调
                if (this.options.onNavClick) {
                    this.options.onNavClick(navType, link);
                }
            });
        });

        // 产品导航点击事件
        const productLinks = container.querySelectorAll('.zc-product-link');
        productLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const productType = link.getAttribute('data-product');
                
                // 更新活跃状态
                this.setActiveProduct(productType);
                
                // 执行回调
                if (this.options.onProductClick) {
                    this.options.onProductClick(productType, link);
                }
            });
        });

        // 联系销售按钮点击事件
        const contactBtn = container.querySelector('.zc-contact-sales-btn');
        if (contactBtn) {
            contactBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (this.options.onContactSales) {
                    this.options.onContactSales(e);
                }
            });
        }
    }

    // 设置初始活跃状态
    setActiveStates() {
        if (this.options.activeNav) {
            this.setActiveNav(this.options.activeNav);
        }
        if (this.options.activeProduct) {
            this.setActiveProduct(this.options.activeProduct);
        }
    }

    // 设置活跃的主导航
    setActiveNav(navType) {
        const container = typeof this.options.container === 'string' 
            ? document.querySelector(this.options.container)
            : this.options.container;

        if (!container) return;

        const navLinks = container.querySelectorAll('.zc-nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-nav') === navType) {
                link.classList.add('active');
            }
        });
    }

    // 设置活跃的产品导航
    setActiveProduct(productType) {
        const container = typeof this.options.container === 'string' 
            ? document.querySelector(this.options.container)
            : this.options.container;

        if (!container) return;

        const productLinks = container.querySelectorAll('.zc-product-link');
        productLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-product') === productType) {
                link.classList.add('active');
            }
        });
    }

    // 更新配置
    updateOptions(newOptions) {
        this.options = { ...this.options, ...newOptions };
        this.setActiveStates();
    }

    // 销毁组件
    destroy() {
        const container = typeof this.options.container === 'string' 
            ? document.querySelector(this.options.container)
            : this.options.container;

        if (container) {
            container.innerHTML = '';
        }
    }
}

// 全局暴露
window.ZCNavigation = ZCNavigation;
